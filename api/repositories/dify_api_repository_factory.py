"""
DifyAPI Repository Factory for creating repository instances.

This factory is specifically designed for DifyAPI repositories that handle
service-layer operations with dependency injection patterns.
"""

import importlib
import logging
from typing import Any

from sqlalchemy.orm import sessionmaker

from configs import dify_config
from repositories.api_workflow_node_execution_repository import DifyAPIWorkflowNodeExecutionRepository

logger = logging.getLogger(__name__)


class RepositoryImportError(Exception):
    """Exception raised when a repository cannot be imported or instantiated."""

    pass


class DifyAPIRepositoryFactory:
    """
    Factory for creating DifyAPI repository instances based on configuration.

    This factory handles the creation of repositories that are specifically designed
    for service-layer operations and use dependency injection with sessionmaker
    for better testability and separation of concerns.
    """

    @classmethod
    def _import_class(cls, class_path: str) -> type[Any]:
        """
        Import a class from a module path.

        Args:
            class_path: Dot-separated path to the class (e.g., 'module.submodule.ClassName')

        Returns:
            The imported class

        Raises:
            RepositoryImportError: If the class cannot be imported
        """
        try:
            module_path, class_name = class_path.rsplit(".", 1)
            module = importlib.import_module(module_path)
            return getattr(module, class_name)  # type: ignore[no-any-return]
        except (ValueError, ModuleNotFoundError, AttributeError) as e:
            raise RepositoryImportError(f"Cannot import class '{class_path}': {e}") from e

    @classmethod
    def _validate_repository_interface(cls, repository_class: type, expected_interface: type) -> None:
        """
        Validate that a repository class implements the expected interface.

        Args:
            repository_class: The repository class to validate
            expected_interface: The expected interface/protocol

        Raises:
            RepositoryImportError: If the repository doesn't implement the interface
        """
        if not issubclass(repository_class, expected_interface):
            raise RepositoryImportError(
                f"Repository class {repository_class.__name__} does not implement {expected_interface.__name__}"
            )

    @classmethod
    def _validate_constructor_signature(cls, repository_class: type, required_params: list[str]) -> None:
        """
        Validate that a repository class constructor has the required parameters.

        Args:
            repository_class: The repository class to validate
            required_params: List of required parameter names

        Raises:
            RepositoryImportError: If the constructor doesn't have required parameters
        """
        import inspect

        try:
            signature = inspect.signature(repository_class.__init__)  # type: ignore[misc]
            param_names = list(signature.parameters.keys())[1:]  # Skip 'self'

            missing_params = [param for param in required_params if param not in param_names]
            if missing_params:
                raise RepositoryImportError(
                    f"Repository {repository_class.__name__} constructor missing required parameters: {missing_params}"
                )
        except Exception as e:
            raise RepositoryImportError(
                f"Cannot validate constructor signature for {repository_class.__name__}: {e}"
            ) from e

    @classmethod
    def create_api_workflow_node_execution_repository(
        cls, session_maker: sessionmaker
    ) -> DifyAPIWorkflowNodeExecutionRepository:
        """
        Create a DifyAPIWorkflowNodeExecutionRepository instance based on configuration.

        This repository is designed for service-layer operations and uses dependency injection
        with a sessionmaker for better testability and separation of concerns. It provides
        database access patterns specifically needed by service classes, handling queries
        that involve database-specific fields and multi-tenancy concerns.

        Args:
            session_maker: SQLAlchemy sessionmaker to inject for database session management.

        Returns:
            Configured DifyAPIWorkflowNodeExecutionRepository instance

        Raises:
            RepositoryImportError: If the configured repository cannot be imported or instantiated
        """
        class_path = dify_config.API_WORKFLOW_NODE_EXECUTION_REPOSITORY
        logger.debug(f"Creating DifyAPIWorkflowNodeExecutionRepository from: {class_path}")

        try:
            repository_class = cls._import_class(class_path)
            cls._validate_repository_interface(repository_class, DifyAPIWorkflowNodeExecutionRepository)
            # Service repository requires session_maker parameter
            cls._validate_constructor_signature(repository_class, ["session_maker"])

            return repository_class(session_maker=session_maker)  # type: ignore[no-any-return]
        except RepositoryImportError:
            # Re-raise our custom errors as-is
            raise
        except Exception as e:
            logger.exception("Failed to create DifyAPIWorkflowNodeExecutionRepository")
            raise RepositoryImportError(
                f"Failed to create DifyAPIWorkflowNodeExecutionRepository from '{class_path}': {e}"
            ) from e
