from datetime import datetime
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest
from sqlalchemy.orm import Session

from models.workflow import WorkflowNodeExecutionModel
from services.sqlalchemy_workflow_node_execution_service_repository import (
    SQLAlchemyWorkflowNodeExecutionServiceRepository,
)


class TestSQLAlchemyWorkflowNodeExecutionServiceRepository:
    @pytest.fixture
    def repository(self):
        return SQLAlchemyWorkflowNodeExecutionServiceRepository()

    @pytest.fixture
    def mock_execution(self):
        execution = MagicMock(spec=WorkflowNodeExecutionModel)
        execution.id = str(uuid4())
        execution.tenant_id = "tenant-123"
        execution.app_id = "app-456"
        execution.workflow_id = "workflow-789"
        execution.workflow_run_id = "run-101"
        execution.node_id = "node-202"
        execution.index = 1
        execution.created_at = "2023-01-01T00:00:00Z"
        return execution

    @patch("services.sqlalchemy_workflow_node_execution_service_repository.db")
    def test_get_node_last_execution_found(self, mock_db, repository, mock_execution):
        """Test getting the last execution for a node when it exists."""
        # Arrange
        mock_session = MagicMock(spec=Session)
        mock_db.session = mock_session
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_execution

        # Act
        result = repository.get_node_last_execution(
            tenant_id="tenant-123",
            app_id="app-456",
            workflow_id="workflow-789",
            node_id="node-202",
        )

        # Assert
        assert result == mock_execution
        mock_session.execute.assert_called_once()
        # Verify the query was constructed correctly
        call_args = mock_session.execute.call_args[0][0]
        assert hasattr(call_args, "compile")  # It's a SQLAlchemy statement

    @patch("services.sqlalchemy_workflow_node_execution_service_repository.db")
    def test_get_node_last_execution_not_found(self, mock_db, repository):
        """Test getting the last execution for a node when it doesn't exist."""
        # Arrange
        mock_session = MagicMock(spec=Session)
        mock_db.session = mock_session
        mock_session.execute.return_value.scalar_one_or_none.return_value = None

        # Act
        result = repository.get_node_last_execution(
            tenant_id="tenant-123",
            app_id="app-456",
            workflow_id="workflow-789",
            node_id="node-202",
        )

        # Assert
        assert result is None
        mock_session.execute.assert_called_once()

    @patch("services.sqlalchemy_workflow_node_execution_service_repository.db")
    def test_get_executions_by_workflow_run(self, mock_db, repository, mock_execution):
        """Test getting all executions for a workflow run."""
        # Arrange
        mock_session = MagicMock(spec=Session)
        mock_db.session = mock_session
        executions = [mock_execution]
        mock_session.execute.return_value.scalars.return_value.all.return_value = executions

        # Act
        result = repository.get_executions_by_workflow_run(
            tenant_id="tenant-123",
            app_id="app-456",
            workflow_run_id="run-101",
        )

        # Assert
        assert result == executions
        mock_session.execute.assert_called_once()
        # Verify the query was constructed correctly
        call_args = mock_session.execute.call_args[0][0]
        assert hasattr(call_args, "compile")  # It's a SQLAlchemy statement

    @patch("services.sqlalchemy_workflow_node_execution_service_repository.db")
    def test_get_executions_by_workflow_run_empty(self, mock_db, repository):
        """Test getting executions for a workflow run when none exist."""
        # Arrange
        mock_session = MagicMock(spec=Session)
        mock_db.session = mock_session
        mock_session.execute.return_value.scalars.return_value.all.return_value = []

        # Act
        result = repository.get_executions_by_workflow_run(
            tenant_id="tenant-123",
            app_id="app-456",
            workflow_run_id="run-101",
        )

        # Assert
        assert result == []
        mock_session.execute.assert_called_once()

    @patch("services.sqlalchemy_workflow_node_execution_service_repository.db")
    def test_get_execution_by_id_found(self, mock_db, repository, mock_execution):
        """Test getting execution by ID when it exists."""
        # Arrange
        mock_session = MagicMock(spec=Session)
        mock_db.session = mock_session
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_execution

        # Act
        result = repository.get_execution_by_id(mock_execution.id)

        # Assert
        assert result == mock_execution
        mock_session.execute.assert_called_once()

    @patch("services.sqlalchemy_workflow_node_execution_service_repository.db")
    def test_get_execution_by_id_not_found(self, mock_db, repository):
        """Test getting execution by ID when it doesn't exist."""
        # Arrange
        mock_session = MagicMock(spec=Session)
        mock_db.session = mock_session
        mock_session.execute.return_value.scalar_one_or_none.return_value = None

        # Act
        result = repository.get_execution_by_id("non-existent-id")

        # Assert
        assert result is None
        mock_session.execute.assert_called_once()

    def test_repository_implements_protocol(self, repository):
        """Test that the repository implements the required protocol methods."""
        # Verify all protocol methods are implemented
        assert hasattr(repository, "get_node_last_execution")
        assert hasattr(repository, "get_executions_by_workflow_run")
        assert hasattr(repository, "get_execution_by_id")

        # Verify methods are callable
        assert callable(repository.get_node_last_execution)
        assert callable(repository.get_executions_by_workflow_run)
        assert callable(repository.get_execution_by_id)
        assert callable(repository.delete_expired_executions)
        assert callable(repository.delete_executions_by_app)

    @patch("services.sqlalchemy_workflow_node_execution_service_repository.db")
    def test_delete_expired_executions(self, mock_db, repository):
        """Test deleting expired executions."""
        # Arrange
        mock_session = MagicMock(spec=Session)
        mock_db.session = mock_session

        # Mock the select query to return some IDs first time, then empty to stop loop
        execution_ids = ["id1", "id2"]  # Less than batch_size to trigger break
        mock_session.execute.return_value.scalars.return_value.all.return_value = execution_ids

        # Mock the delete query
        mock_query = MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value.delete.return_value = 2

        before_date = datetime(2023, 1, 1)

        # Act
        result = repository.delete_expired_executions(
            tenant_id="tenant-123",
            before_date=before_date,
            batch_size=1000,
        )

        # Assert
        assert result == 2
        mock_session.execute.assert_called_once()  # One select call
        mock_session.query.assert_called_once()
        mock_session.commit.assert_called_once()

    @patch("services.sqlalchemy_workflow_node_execution_service_repository.db")
    def test_delete_executions_by_app(self, mock_db, repository):
        """Test deleting executions by app."""
        # Arrange
        mock_session = MagicMock(spec=Session)
        mock_db.session = mock_session

        # Mock the select query to return some IDs first time, then empty to stop loop
        execution_ids = ["id1", "id2"]
        mock_session.execute.return_value.scalars.return_value.all.return_value = execution_ids

        # Mock the delete query
        mock_query = MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value.delete.return_value = 2

        # Act
        result = repository.delete_executions_by_app(
            tenant_id="tenant-123",
            app_id="app-456",
            batch_size=1000,
        )

        # Assert
        assert result == 2
        mock_session.execute.assert_called_once()  # One select call
        mock_session.query.assert_called_once()
        mock_session.commit.assert_called_once()
